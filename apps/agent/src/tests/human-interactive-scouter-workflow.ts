import { creatorScoutWorkflow } from '@/workflows/creatorScoutWorkflow';
import { <PERSON><PERSON> } from '@mastra/core';
import { input } from '@inquirer/prompts';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';
import { creatorFilterAgent } from '@/agents/creatorFilterAgent';
import { challengePickerAgent } from '@/agents/challengePickerAgent';
import {
  saveCreatorResultsToFile,
  saveCreatorResultsWithFormat,
} from '@/utils/saveResults';

// Register the workflow
const mastra = new Mastra({
  agents: { creatorHashtagScout, creatorFilterAgent, challengePickerAgent },
  workflows: { creatorScoutWorkflow },
});

async function runKOLScouterDemo() {
  const registeredWorkflow = mastra.getWorkflow('creatorScoutWorkflow');
  const run = registeredWorkflow.createRun();

  // Start the workflow with content that needs review
  console.log('Starting kol scouter workflow...');

  run.watch(async (watchEvents) => {
    // console.log('=== WATCH EVENT TRIGGERED ===');
    // console.log('Workflow state:', watchEvents.payload.workflowState);

    // TODO: THIS MIGHT BE A BUG FROM MASTRA.
    // Only process when workflow is in the 'running' state, not 'suspended'. But we do need that step to be suspended for the demo

    if (watchEvents.payload.workflowState.status !== 'running') {
      return;
    }
    const steps = Object.entries(watchEvents.payload.workflowState.steps);

    for (const [stepId, step] of steps) {
      const path = step;
      // console.log(`Step ${stepId} status:`, path?.status);

      if (path && path.status === 'suspended') {
        const { message, messages } = path.payload!;
        console.log('Messages count:', messages.length);
        console.log('\n===================================');
        console.log(message.text);
        console.log('===================================\n');

        const answer = await input({
          message: 'Please enter your response:',
        });

        console.log('user responded:', answer);

        await run.resume({
          step: stepId,
          resumeData: {
            messages,
            userInputMessage: answer,
          },
        });

        console.log('resumed');
      }
    }
  });

  const inputData = {
    targetCreatorDescription:
      'I need to find some American gaming creators with following rules: 1. They post videos about various games, not just one game. 2. The median view count of their recent videos is greater than 50,000. 3. They speak English. 4. They show their faces and have voiceovers in their videos.',
    // 'I want to find kols who can promote the mobile game "Whiteout Survival" in Japan',
    // '一家智能投影仪品牌计划在中东市场进行内容投放，目标人群为35岁以下、有独立居住空间的科技早期用户，主要通过生活方式类博主触达。',
    useIntelligentChallengeSelection: true, // Test the intelligent selection
    desiredCreatorCount: 30, // Test with 30 creators
    filterMode: 'STRICT' as const,
    saveResultsToFile: false, // We'll save manually with multiple formats
  };

  const result = await run.start({
    inputData,
  });

  console.log('Final output:', JSON.stringify(result, null, 2));

  // Save results to local JSON file in multiple formats
  try {
    // Save detailed results
    const detailedFilePath = saveCreatorResultsToFile(result, inputData);

    // Save summary format
    const summaryFilePath = saveCreatorResultsWithFormat(
      result,
      inputData,
      'summary',
    );

    // Save CSV-ready format
    const csvFilePath = saveCreatorResultsWithFormat(
      result,
      inputData,
      'csv-ready',
    );

    console.log(`\n🎉 Workflow completed successfully!`);
    console.log(`📄 Detailed results saved to: ${detailedFilePath}`);
    console.log(`📋 Summary saved to: ${summaryFilePath}`);
    console.log(`📊 CSV-ready data saved to: ${csvFilePath}`);
  } catch (error) {
    console.error('Failed to save results to file:', error);
  }

  // let isStepSuspended =
  //   result.activePaths.get('analyzeRequirement')?.status === 'suspended';

  // while (isStepSuspended) {
  //   const { message, messages } =
  //     result.activePaths.get('analyzeRequirement')?.suspendPayload;

  //   console.log('\n===================================');
  //   console.log(message);
  //   console.log('===================================\n');

  //   const answer = await input({
  //     message: 'Please enter your response:',
  //   });

  //   await run.resume({
  //     stepId: 'analyzeRequirement',
  //     context: {
  //       messages: messages,
  //       userInputMessage: answer,
  //     },
  //   });

  //   isStepSuspended =
  //     result.activePaths.get('analyzeRequirement')?.status === 'suspended';
  // }

  // console.log('Final output:', result.results);
}

runKOLScouterDemo().catch(console.error);
